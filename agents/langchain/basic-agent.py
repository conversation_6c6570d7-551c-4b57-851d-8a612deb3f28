#!/usr/bin/env python3
"""
LangChain基础AI Agent示例
演示如何创建一个简单的AI助手，具备工具调用和对话能力
"""

import os
from typing import List, Dict, Any
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain.tools import Tool
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import HumanMessage, AIMessage
import requests
import json

# 配置API密钥
os.environ["OPENAI_API_KEY"] = "your-openai-api-key"

class WeatherTool:
    """天气查询工具"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "http://api.openweathermap.org/data/2.5/weather"
    
    def get_weather(self, city: str) -> str:
        """获取指定城市的天气信息"""
        try:
            params = {
                "q": city,
                "appid": self.api_key,
                "units": "metric",
                "lang": "zh_cn"
            }
            response = requests.get(self.base_url, params=params)
            data = response.json()
            
            if response.status_code == 200:
                weather = data["weather"][0]["description"]
                temp = data["main"]["temp"]
                feels_like = data["main"]["feels_like"]
                humidity = data["main"]["humidity"]
                
                return f"{city}的天气：{weather}，温度{temp}°C，体感温度{feels_like}°C，湿度{humidity}%"
            else:
                return f"无法获取{city}的天气信息：{data.get('message', '未知错误')}"
        except Exception as e:
            return f"天气查询出错：{str(e)}"

class CalculatorTool:
    """计算器工具"""
    
    def calculate(self, expression: str) -> str:
        """安全地计算数学表达式"""
        try:
            # 只允许基本的数学运算
            allowed_chars = set('0123456789+-*/.() ')
            if not all(c in allowed_chars for c in expression):
                return "表达式包含不允许的字符"
            
            result = eval(expression)
            return f"{expression} = {result}"
        except Exception as e:
            return f"计算错误：{str(e)}"

class BasicAgent:
    """基础AI Agent类"""
    
    def __init__(self, model_name: str = "gpt-3.5-turbo"):
        self.llm = ChatOpenAI(model=model_name, temperature=0.7)
        self.tools = self._create_tools()
        self.agent = self._create_agent()
        self.executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True
        )
        self.chat_history = []
    
    def _create_tools(self) -> List[Tool]:
        """创建工具列表"""
        weather_tool = WeatherTool("your-weather-api-key")  # 需要替换为实际的API密钥
        calculator_tool = CalculatorTool()
        
        tools = [
            Tool(
                name="weather",
                description="获取指定城市的天气信息。输入应该是城市名称，如'北京'或'上海'",
                func=weather_tool.get_weather
            ),
            Tool(
                name="calculator",
                description="计算数学表达式。输入应该是有效的数学表达式，如'2+3*4'",
                func=calculator_tool.calculate
            ),
            Tool(
                name="search_knowledge",
                description="搜索知识库信息。输入应该是要搜索的问题或关键词",
                func=self._search_knowledge
            )
        ]
        
        return tools
    
    def _search_knowledge(self, query: str) -> str:
        """模拟知识库搜索"""
        knowledge_base = {
            "python": "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。",
            "ai": "人工智能(AI)是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            "langchain": "LangChain是一个用于开发由语言模型驱动的应用程序的框架。",
            "机器学习": "机器学习是人工智能的一个子集，使计算机能够在没有明确编程的情况下学习和改进。"
        }
        
        query_lower = query.lower()
        for key, value in knowledge_base.items():
            if key in query_lower or query_lower in key:
                return f"关于'{query}'的信息：{value}"
        
        return f"抱歉，没有找到关于'{query}'的相关信息。"
    
    def _create_agent(self):
        """创建AI Agent"""
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个有用的AI助手。你可以使用以下工具来帮助用户：
            1. weather - 查询天气信息
            2. calculator - 进行数学计算
            3. search_knowledge - 搜索知识库
            
            请根据用户的问题选择合适的工具，并提供有用的回答。
            如果不需要使用工具，可以直接回答用户的问题。"""),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        return create_openai_functions_agent(self.llm, self.tools, prompt)
    
    def chat(self, message: str) -> str:
        """与Agent对话"""
        try:
            response = self.executor.invoke({
                "input": message,
                "chat_history": self.chat_history
            })
            
            # 更新对话历史
            self.chat_history.extend([
                HumanMessage(content=message),
                AIMessage(content=response["output"])
            ])
            
            return response["output"]
        except Exception as e:
            return f"处理请求时出错：{str(e)}"
    
    def clear_history(self):
        """清空对话历史"""
        self.chat_history = []

def main():
    """主函数 - 演示Agent的使用"""
    print("🤖 LangChain基础AI Agent示例")
    print("=" * 50)
    
    # 创建Agent实例
    agent = BasicAgent()
    
    # 示例对话
    test_queries = [
        "你好，你能做什么？",
        "帮我计算 15 * 8 + 32",
        "北京今天的天气怎么样？",
        "告诉我关于Python的信息",
        "什么是机器学习？"
    ]
    
    for query in test_queries:
        print(f"\n👤 用户: {query}")
        response = agent.chat(query)
        print(f"🤖 助手: {response}")
        print("-" * 30)
    
    # 交互式对话模式
    print("\n🎯 进入交互模式 (输入 'quit' 退出):")
    while True:
        user_input = input("\n👤 你: ").strip()
        if user_input.lower() in ['quit', 'exit', '退出']:
            break
        
        if user_input:
            response = agent.chat(user_input)
            print(f"🤖 助手: {response}")

if __name__ == "__main__":
    main()
