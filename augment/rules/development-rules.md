# Augment AI 开发规则配置

## 代码生成规则

### 基础原则
- 优先使用现代框架和最佳实践
- 遵循项目现有的代码风格和架构
- 生成的代码必须包含适当的错误处理
- 所有函数和类都需要文档字符串

### 语言特定规则

#### Python
```python
# 使用类型提示
def process_data(data: List[Dict[str, Any]]) -> Optional[DataFrame]:
    """处理数据并返回DataFrame
    
    Args:
        data: 输入数据列表
        
    Returns:
        处理后的DataFrame，失败时返回None
    """
    pass

# 使用现代Python特性
from pathlib import Path
from dataclasses import dataclass
from typing import Optional, List, Dict, Any
```

#### JavaScript/TypeScript
```typescript
// 优先使用TypeScript
interface UserData {
  id: string;
  name: string;
  email?: string;
}

// 使用现代ES6+语法
const processUsers = async (users: UserData[]): Promise<UserData[]> => {
  return users.filter(user => user.email);
};
```

## 项目结构规则

### 目录组织
```
project/
├── src/                 # 源代码
│   ├── components/      # 组件
│   ├── utils/          # 工具函数
│   ├── types/          # 类型定义
│   └── tests/          # 测试文件
├── docs/               # 文档
├── config/             # 配置文件
└── scripts/            # 脚本文件
```

### 命名约定
- 文件名：kebab-case (user-profile.ts)
- 类名：PascalCase (UserProfile)
- 函数名：camelCase (getUserProfile)
- 常量：UPPER_SNAKE_CASE (MAX_RETRY_COUNT)

## 测试规则

### 测试覆盖率
- 核心业务逻辑：100%覆盖率
- 工具函数：90%以上覆盖率
- UI组件：主要交互路径覆盖

### 测试结构
```python
def test_function_name():
    # Arrange - 准备测试数据
    input_data = {"key": "value"}
    
    # Act - 执行被测试的功能
    result = function_under_test(input_data)
    
    # Assert - 验证结果
    assert result == expected_output
```

## 安全规则

### 数据处理
- 永远不要在日志中记录敏感信息
- 使用参数化查询防止SQL注入
- 验证所有用户输入
- 使用HTTPS进行数据传输

### API设计
- 实现适当的认证和授权
- 使用速率限制防止滥用
- 返回适当的HTTP状态码
- 不在错误消息中暴露内部信息

## 性能规则

### 数据库查询
- 使用索引优化查询性能
- 避免N+1查询问题
- 实现适当的缓存策略
- 使用连接池管理数据库连接

### 前端性能
- 实现代码分割和懒加载
- 优化图片和静态资源
- 使用CDN加速资源加载
- 实现适当的缓存策略

## 文档规则

### 代码注释
- 解释"为什么"而不是"是什么"
- 复杂算法需要详细注释
- 使用TODO标记待完成的工作
- 及时更新过时的注释

### API文档
- 使用OpenAPI/Swagger规范
- 提供完整的请求/响应示例
- 说明错误码和处理方式
- 包含认证和授权信息

## 版本控制规则

### 提交消息格式
```
type(scope): description

feat(auth): add user authentication
fix(api): resolve data validation issue
docs(readme): update installation guide
```

### 分支策略
- main: 生产环境代码
- develop: 开发环境代码
- feature/*: 功能开发分支
- hotfix/*: 紧急修复分支
