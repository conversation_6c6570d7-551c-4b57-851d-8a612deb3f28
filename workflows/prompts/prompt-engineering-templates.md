# AI辅助开发Prompt工程模板

## 基础Prompt结构

### 标准模板
```
角色定义: 你是一个[专业领域]专家
任务描述: 请帮我[具体任务]
上下文信息: [相关背景信息]
输出要求: [期望的输出格式和质量标准]
约束条件: [限制和注意事项]
```

### 示例应用
```
角色定义: 你是一个Python后端开发专家
任务描述: 请帮我设计一个用户认证系统的API
上下文信息: 使用FastAPI框架，需要支持JWT令牌，包含注册、登录、刷新令牌功能
输出要求: 提供完整的代码实现，包含错误处理和安全最佳实践
约束条件: 代码需要遵循PEP8规范，包含类型提示和文档字符串
```

## 代码生成Prompt模板

### 1. 功能实现模板
```
请实现一个[功能名称]功能：

**需求描述:**
- [具体需求1]
- [具体需求2]
- [具体需求3]

**技术栈:**
- 语言: [编程语言]
- 框架: [使用的框架]
- 数据库: [数据库类型]

**代码要求:**
- 遵循[编码规范]
- 包含错误处理
- 添加单元测试
- 性能优化考虑

**输出格式:**
请提供完整的代码实现，包括：
1. 主要功能代码
2. 测试用例
3. 使用示例
4. 部署说明
```

### 2. 代码重构模板
```
请帮我重构以下代码：

**原始代码:**
```[语言]
[粘贴原始代码]
```

**重构目标:**
- [目标1: 如提高可读性]
- [目标2: 如优化性能]
- [目标3: 如增强可维护性]

**约束条件:**
- 保持原有功能不变
- 遵循[设计模式/原则]
- 向后兼容

**期望输出:**
1. 重构后的代码
2. 重构说明和理由
3. 性能对比分析
4. 迁移指南
```

### 3. 调试协助模板
```
我遇到了一个[问题类型]问题，请帮我分析和解决：

**问题描述:**
[详细描述问题现象]

**错误信息:**
```
[粘贴完整的错误堆栈]
```

**相关代码:**
```[语言]
[粘贴相关代码片段]
```

**环境信息:**
- 操作系统: [OS版本]
- 语言版本: [语言版本]
- 依赖版本: [关键依赖版本]

**已尝试的解决方案:**
- [尝试1]
- [尝试2]

**期望输出:**
1. 问题根因分析
2. 具体解决方案
3. 预防措施建议
4. 相关最佳实践
```

## 架构设计Prompt模板

### 系统架构设计
```
请帮我设计一个[系统类型]的架构：

**业务需求:**
- 用户规模: [预期用户数量]
- 数据量: [预期数据规模]
- 性能要求: [QPS、延迟等指标]
- 可用性要求: [SLA要求]

**技术约束:**
- 预算限制: [成本考虑]
- 技术栈偏好: [首选技术]
- 部署环境: [云平台/本地部署]
- 团队技能: [团队技术背景]

**输出要求:**
1. 整体架构图
2. 技术选型说明
3. 数据流设计
4. 部署架构
5. 扩展性考虑
6. 风险评估
```

## 学习辅助Prompt模板

### 概念解释模板
```
请用通俗易懂的方式解释[技术概念]：

**解释要求:**
- 目标受众: [初学者/中级/高级]
- 包含实际例子
- 对比相似概念
- 说明应用场景

**输出结构:**
1. 简单定义
2. 核心原理
3. 实际例子
4. 优缺点分析
5. 学习资源推荐
```

### 技术对比模板
```
请对比分析以下技术方案：

**对比对象:**
- 方案A: [技术A]
- 方案B: [技术B]
- 方案C: [技术C]

**对比维度:**
- 性能表现
- 学习成本
- 社区支持
- 生态完整性
- 适用场景

**使用场景:**
[描述具体的应用场景和需求]

**输出格式:**
| 维度 | 方案A | 方案B | 方案C |
|------|-------|-------|-------|
| 性能 | ... | ... | ... |
| 成本 | ... | ... | ... |

最终推荐: [基于场景的推荐方案和理由]
```

## 代码审查Prompt模板

### 代码质量审查
```
请审查以下代码的质量：

**代码:**
```[语言]
[粘贴代码]
```

**审查维度:**
- 代码规范性
- 性能优化
- 安全性
- 可维护性
- 测试覆盖

**输出要求:**
1. 问题清单 (按严重程度排序)
2. 改进建议 (具体的修改方案)
3. 最佳实践建议
4. 重构优先级
```

## 文档生成Prompt模板

### API文档生成
```
请为以下代码生成API文档：

**代码:**
```[语言]
[粘贴API代码]
```

**文档要求:**
- 遵循OpenAPI 3.0规范
- 包含请求/响应示例
- 错误码说明
- 认证方式说明

**输出格式:**
使用Markdown格式，包含：
1. API概述
2. 认证说明
3. 端点详情
4. 数据模型
5. 错误处理
6. 使用示例
```

## 测试用例生成模板

### 单元测试生成
```
请为以下函数生成完整的单元测试：

**函数代码:**
```[语言]
[粘贴函数代码]
```

**测试要求:**
- 覆盖所有分支
- 包含边界条件测试
- 异常情况处理
- 使用[测试框架名称]

**输出包含:**
1. 测试用例代码
2. 测试数据准备
3. Mock对象设置
4. 断言验证
5. 测试覆盖率说明
```

## 性能优化Prompt模板

### 性能分析模板
```
请分析以下代码的性能问题并提供优化方案：

**代码:**
```[语言]
[粘贴代码]
```

**性能指标:**
- 当前响应时间: [具体数值]
- 内存使用: [具体数值]
- CPU使用率: [具体数值]
- 目标性能: [期望指标]

**分析要求:**
1. 性能瓶颈识别
2. 优化方案设计
3. 预期性能提升
4. 实施风险评估
5. 监控指标建议
```

## 最佳实践

### Prompt优化技巧
1. **具体化**: 避免模糊的描述，提供具体的需求和约束
2. **结构化**: 使用清晰的格式和分段，便于AI理解
3. **示例驱动**: 提供期望输出的示例格式
4. **迭代改进**: 根据输出质量调整prompt内容
5. **上下文管理**: 合理控制上下文长度，突出重点信息

### 常见问题避免
- 避免过于宽泛的要求
- 不要忽略技术约束说明
- 确保提供足够的背景信息
- 明确指定输出格式和质量标准
