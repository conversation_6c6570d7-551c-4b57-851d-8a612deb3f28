#!/usr/bin/env python3
"""
AI开发工具配置验证脚本
用于验证各种AI开发工具的配置是否正确
"""

import os
import sys
import json
import subprocess
import requests
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import importlib.util

class Colors:
    """终端颜色常量"""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class VerificationResult:
    """验证结果类"""
    def __init__(self, name: str, success: bool, message: str, details: Optional[str] = None):
        self.name = name
        self.success = success
        self.message = message
        self.details = details

class AIToolsVerifier:
    """AI开发工具验证器"""
    
    def __init__(self):
        self.results: List[VerificationResult] = []
        self.project_root = Path.cwd()
    
    def print_header(self, title: str):
        """打印标题"""
        print(f"\n{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.BLUE}{title:^60}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")
    
    def print_result(self, result: VerificationResult):
        """打印验证结果"""
        status_color = Colors.GREEN if result.success else Colors.RED
        status_symbol = "✓" if result.success else "✗"
        
        print(f"{status_color}{status_symbol} {result.name}: {result.message}{Colors.END}")
        if result.details:
            print(f"  {Colors.CYAN}详情: {result.details}{Colors.END}")
    
    def verify_python_environment(self) -> List[VerificationResult]:
        """验证Python环境"""
        results = []
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version >= (3, 8):
            results.append(VerificationResult(
                "Python版本", True, 
                f"Python {python_version.major}.{python_version.minor}.{python_version.micro}"
            ))
        else:
            results.append(VerificationResult(
                "Python版本", False,
                f"Python版本过低: {python_version.major}.{python_version.minor}，建议3.8+"
            ))
        
        # 检查关键包
        required_packages = [
            ("requests", "HTTP请求库"),
            ("openai", "OpenAI API客户端"),
            ("langchain", "LangChain框架"),
            ("anthropic", "Anthropic API客户端")
        ]
        
        for package, description in required_packages:
            try:
                spec = importlib.util.find_spec(package)
                if spec is not None:
                    results.append(VerificationResult(
                        f"包: {package}", True, f"{description} 已安装"
                    ))
                else:
                    results.append(VerificationResult(
                        f"包: {package}", False, f"{description} 未安装"
                    ))
            except Exception as e:
                results.append(VerificationResult(
                    f"包: {package}", False, f"检查失败: {str(e)}"
                ))
        
        return results
    
    def verify_environment_variables(self) -> List[VerificationResult]:
        """验证环境变量"""
        results = []
        
        env_vars = [
            ("OPENAI_API_KEY", "OpenAI API密钥"),
            ("ANTHROPIC_API_KEY", "Anthropic API密钥"),
            ("GITHUB_TOKEN", "GitHub访问令牌"),
            ("BRAVE_API_KEY", "Brave搜索API密钥")
        ]
        
        for var_name, description in env_vars:
            value = os.getenv(var_name)
            if value:
                masked_value = f"{value[:8]}..." if len(value) > 8 else "***"
                results.append(VerificationResult(
                    f"环境变量: {var_name}", True, 
                    f"{description} 已设置 ({masked_value})"
                ))
            else:
                results.append(VerificationResult(
                    f"环境变量: {var_name}", False,
                    f"{description} 未设置"
                ))
        
        return results
    
    def verify_mcp_configuration(self) -> List[VerificationResult]:
        """验证MCP配置"""
        results = []
        
        # 检查Claude Desktop配置文件
        config_paths = [
            Path.home() / "Library/Application Support/Claude/claude_desktop_config.json",  # macOS
            Path.home() / ".config/claude/claude_desktop_config.json",  # Linux
            Path.home() / "AppData/Roaming/Claude/claude_desktop_config.json"  # Windows
        ]
        
        config_found = False
        for config_path in config_paths:
            if config_path.exists():
                config_found = True
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    if "mcpServers" in config:
                        server_count = len(config["mcpServers"])
                        results.append(VerificationResult(
                            "Claude Desktop配置", True,
                            f"找到配置文件，包含{server_count}个MCP服务器"
                        ))
                    else:
                        results.append(VerificationResult(
                            "Claude Desktop配置", False,
                            "配置文件存在但缺少mcpServers配置"
                        ))
                except Exception as e:
                    results.append(VerificationResult(
                        "Claude Desktop配置", False,
                        f"配置文件解析失败: {str(e)}"
                    ))
                break
        
        if not config_found:
            results.append(VerificationResult(
                "Claude Desktop配置", False,
                "未找到Claude Desktop配置文件"
            ))
        
        # 检查本地MCP配置
        local_config = self.project_root / "mcp/servers/claude-desktop-config.json"
        if local_config.exists():
            results.append(VerificationResult(
                "本地MCP配置", True,
                "找到本地MCP配置模板"
            ))
        else:
            results.append(VerificationResult(
                "本地MCP配置", False,
                "未找到本地MCP配置模板"
            ))
        
        return results
    
    def verify_augment_configuration(self) -> List[VerificationResult]:
        """验证Augment配置"""
        results = []
        
        # 检查规则文件
        rules_path = self.project_root / "augment/rules/development-rules.md"
        if rules_path.exists():
            results.append(VerificationResult(
                "Augment规则文件", True,
                "找到开发规则配置文件"
            ))
        else:
            results.append(VerificationResult(
                "Augment规则文件", False,
                "未找到开发规则配置文件"
            ))
        
        # 检查示例文件
        examples_dir = self.project_root / "augment/examples"
        if examples_dir.exists():
            example_count = len(list(examples_dir.glob("*")))
            results.append(VerificationResult(
                "Augment示例", True,
                f"找到{example_count}个示例文件"
            ))
        else:
            results.append(VerificationResult(
                "Augment示例", False,
                "未找到Augment示例目录"
            ))
        
        return results
    
    def verify_agent_frameworks(self) -> List[VerificationResult]:
        """验证AI Agent框架"""
        results = []
        
        # 检查LangChain示例
        langchain_example = self.project_root / "agents/langchain/basic-agent.py"
        if langchain_example.exists():
            results.append(VerificationResult(
                "LangChain示例", True,
                "找到LangChain基础Agent示例"
            ))
        else:
            results.append(VerificationResult(
                "LangChain示例", False,
                "未找到LangChain示例文件"
            ))
        
        # 检查其他框架目录
        frameworks = ["crewai", "autogen", "semantic-kernel"]
        for framework in frameworks:
            framework_dir = self.project_root / "agents" / framework
            if framework_dir.exists():
                results.append(VerificationResult(
                    f"{framework}目录", True,
                    f"找到{framework}框架目录"
                ))
            else:
                results.append(VerificationResult(
                    f"{framework}目录", False,
                    f"未找到{framework}框架目录"
                ))
        
        return results
    
    def verify_workflow_templates(self) -> List[VerificationResult]:
        """验证工作流模板"""
        results = []
        
        # 检查Prompt模板
        prompt_templates = self.project_root / "workflows/prompts/prompt-engineering-templates.md"
        if prompt_templates.exists():
            results.append(VerificationResult(
                "Prompt工程模板", True,
                "找到Prompt工程模板文件"
            ))
        else:
            results.append(VerificationResult(
                "Prompt工程模板", False,
                "未找到Prompt工程模板文件"
            ))
        
        # 检查IDE集成配置
        ide_config_dir = self.project_root / "workflows/ide-integration"
        if ide_config_dir.exists():
            results.append(VerificationResult(
                "IDE集成配置", True,
                "找到IDE集成配置目录"
            ))
        else:
            results.append(VerificationResult(
                "IDE集成配置", False,
                "未找到IDE集成配置目录"
            ))
        
        return results
    
    def verify_api_connectivity(self) -> List[VerificationResult]:
        """验证API连接性"""
        results = []
        
        # 测试OpenAI API
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key:
            try:
                headers = {"Authorization": f"Bearer {openai_key}"}
                response = requests.get(
                    "https://api.openai.com/v1/models",
                    headers=headers,
                    timeout=10
                )
                if response.status_code == 200:
                    results.append(VerificationResult(
                        "OpenAI API连接", True,
                        "API连接正常"
                    ))
                else:
                    results.append(VerificationResult(
                        "OpenAI API连接", False,
                        f"API返回错误: {response.status_code}"
                    ))
            except Exception as e:
                results.append(VerificationResult(
                    "OpenAI API连接", False,
                    f"连接失败: {str(e)}"
                ))
        else:
            results.append(VerificationResult(
                "OpenAI API连接", False,
                "未设置API密钥，跳过连接测试"
            ))
        
        return results
    
    def run_all_verifications(self):
        """运行所有验证"""
        self.print_header("AI开发工具配置验证")
        
        verification_groups = [
            ("Python环境", self.verify_python_environment),
            ("环境变量", self.verify_environment_variables),
            ("MCP配置", self.verify_mcp_configuration),
            ("Augment配置", self.verify_augment_configuration),
            ("Agent框架", self.verify_agent_frameworks),
            ("工作流模板", self.verify_workflow_templates),
            ("API连接性", self.verify_api_connectivity)
        ]
        
        all_results = []
        for group_name, verify_func in verification_groups:
            print(f"\n{Colors.BOLD}{Colors.PURPLE}🔍 {group_name}{Colors.END}")
            group_results = verify_func()
            for result in group_results:
                self.print_result(result)
                all_results.append(result)
        
        # 统计结果
        total_checks = len(all_results)
        passed_checks = sum(1 for r in all_results if r.success)
        failed_checks = total_checks - passed_checks
        
        print(f"\n{Colors.BOLD}{Colors.WHITE}📊 验证结果统计{Colors.END}")
        print(f"{Colors.GREEN}✓ 通过: {passed_checks}{Colors.END}")
        print(f"{Colors.RED}✗ 失败: {failed_checks}{Colors.END}")
        print(f"{Colors.BLUE}📈 成功率: {passed_checks/total_checks*100:.1f}%{Colors.END}")
        
        if failed_checks > 0:
            print(f"\n{Colors.YELLOW}⚠️  建议检查失败的配置项并参考文档进行修复{Colors.END}")
        else:
            print(f"\n{Colors.GREEN}🎉 所有配置验证通过！{Colors.END}")

def main():
    """主函数"""
    verifier = AIToolsVerifier()
    verifier.run_all_verifications()

if __name__ == "__main__":
    main()
