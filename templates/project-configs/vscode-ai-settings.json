{"// AI开发工具VS Code配置模板": "适用于集成多种AI工具的开发环境", "// GitHub Copilot配置": "", "github.copilot.enable": {"*": true, "yaml": false, "plaintext": false, "markdown": true, "json": true}, "github.copilot.advanced": {"listCount": 10, "inlineSuggestCount": 3, "debug.overrideEngine": "codex"}, "github.copilot.editor.enableAutoCompletions": true, "github.copilot.chat.localeOverride": "zh-CN", "// 代码格式化和质量": "", "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll": true, "source.organizeImports": true, "source.addMissingImports": true}, "// AI辅助开发增强": "", "editor.suggestSelection": "first", "editor.tabCompletion": "on", "editor.wordBasedSuggestions": true, "editor.quickSuggestions": {"other": true, "comments": true, "strings": true}, "editor.acceptSuggestionOnCommitCharacter": true, "editor.acceptSuggestionOnEnter": "on", "// 智能感知配置": "", "typescript.suggest.autoImports": true, "typescript.suggest.includeCompletionsForModuleExports": true, "python.analysis.autoImportCompletions": true, "python.analysis.completeFunctionParens": true, "// 文件关联和语言支持": "", "files.associations": {"*.env.example": "properties", "*.env.local": "properties", "*.env.development": "properties", "*.env.production": "properties", ".augment-guidelines": "markdown", ".clinerules": "markdown"}, "// 终端和集成工具": "", "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.enableMultiLinePasteWarning": false, "// Git集成": "", "git.enableSmartCommit": true, "git.autofetch": true, "git.confirmSync": false, "git.enableCommitSigning": true, "// 工作区信任": "", "security.workspace.trust.untrustedFiles": "open", "security.workspace.trust.banner": "never", "// 扩展特定配置": "", "// Augment扩展配置": "", "augment.enableContextualSuggestions": true, "augment.maxContextLength": 8000, "augment.enableMemory": true, "augment.rulesPath": ".augment/rules", "// Cursor相关配置": "", "cursor.cpp.disabledLanguages": [], "cursor.general.enableHoverActions": true, "// 语言特定配置": "", "// Python": "", "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}}, "python.defaultInterpreterPath": "./venv/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.testing.pytestEnabled": true, "// JavaScript/TypeScript": "", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "// JSON和配置文件": "", "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.quickSuggestions": {"strings": true}}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "// Markdown": "", "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "on", "editor.quickSuggestions": {"comments": "off", "strings": "off", "other": "off"}}, "// YAML": "", "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.insertSpaces": true, "editor.tabSize": 2}, "// 性能优化": "", "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/venv": true, "**/__pycache__": true, "**/.pytest_cache": true, "**/dist": true, "**/build": true, "**/.next": true, "**/.nuxt": true}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/venv/**": true, "**/__pycache__/**": true}, "// 主题和外观": "", "workbench.colorTheme": "GitHub Dark Default", "workbench.iconTheme": "material-icon-theme", "editor.fontFamily": "'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace", "editor.fontLigatures": true, "editor.fontSize": 14, "editor.lineHeight": 1.5, "// 调试配置": "", "debug.console.fontSize": 14, "debug.internalConsoleOptions": "openOnSessionStart", "// 实时分享和协作": "", "liveshare.featureSet": "stable", "liveshare.guestApprovalRequired": true, "// AI工具集成验证": "", "// 这些设置用于验证AI工具是否正确集成": "", "ai.tools.verification": {"copilot": "github.copilot.enable", "augment": "augment.enableContextualSuggestions", "cursor": "cursor.general.enableHoverActions"}, "// 自定义快捷键提示": "", "// 建议的AI相关快捷键": "", "ai.shortcuts.suggestions": {"copilot.chat": "Ctrl+Shift+I", "copilot.explain": "Ctrl+Shift+E", "copilot.fix": "Ctrl+Shift+F", "augment.ask": "Ctrl+Shift+A", "cursor.chat": "Ctrl+L"}, "// 项目特定配置提示": "", "// 在项目根目录创建.vscode/settings.json覆盖这些设置": "", "project.config.override": {"note": "项目特定设置应放在工作区的.vscode/settings.json中", "examples": {"python.defaultInterpreterPath": "./venv/bin/python", "augment.rulesPath": "./project-specific-rules", "github.copilot.enable": {"sensitive-file-pattern": false}}}}