{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"], "description": "文件系统访问服务器"}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key"}, "description": "Brave搜索引擎集成"}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token"}, "description": "GitHub仓库访问"}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/dbname"}, "description": "PostgreSQL数据库连接"}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "/path/to/database.db"], "description": "SQLite数据库访问"}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "description": "网页自动化和截图"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "description": "持久化记忆存储"}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "description": "HTTP请求工具"}, "everart": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everart"], "env": {"EVERART_API_KEY": "your-everart-api-key"}, "description": "AI图像生成服务"}, "gdrive": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdrive"], "env": {"GOOGLE_CLIENT_ID": "your-client-id", "GOOGLE_CLIENT_SECRET": "your-client-secret"}, "description": "Google Drive文件访问"}, "gmail": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gmail"], "env": {"GOOGLE_CLIENT_ID": "your-client-id", "GOOGLE_CLIENT_SECRET": "your-client-secret"}, "description": "Gmail邮件管理"}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "xoxb-your-bot-token"}, "description": "Slack消息和频道管理"}, "youtube-transcript": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-youtube-transcript"], "description": "YouTube视频字幕提取"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "结构化思维工具"}, "time": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "description": "时间和日期工具"}, "obsidian": {"command": "node", "args": ["/path/to/mcp-obsidian/dist/index.js"], "env": {"OBSIDIAN_VAULT_PATH": "/path/to/your/obsidian/vault"}, "description": "Obsidian笔记管理"}, "notion": {"command": "node", "args": ["/path/to/notion-mcp/build/index.js"], "env": {"NOTION_API_KEY": "your-notion-integration-token"}, "description": "Notion数据库和页面管理"}, "docker": {"command": "npx", "args": ["-y", "mcp-server-docker"], "description": "Docker容器管理"}, "kubernetes": {"command": "npx", "args": ["-y", "mcp-server-kubernetes"], "env": {"KUBECONFIG": "/path/to/kubeconfig"}, "description": "Kubernetes集群管理"}, "aws": {"command": "npx", "args": ["-y", "mcp-server-aws"], "env": {"AWS_ACCESS_KEY_ID": "your-access-key", "AWS_SECRET_ACCESS_KEY": "your-secret-key", "AWS_REGION": "us-west-2"}, "description": "AWS云服务管理"}}}