#!/usr/bin/env python3
"""
MCP服务器验证工具
检查MCP服务器的安装状态和可用性
"""

import json
import subprocess
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import requests
import time

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class MCPServerValidator:
    def __init__(self, config_path: str):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.results = {}
    
    def _load_config(self) -> Dict:
        """加载MCP配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"{Colors.RED}❌ 无法加载配置文件: {e}{Colors.END}")
            sys.exit(1)
    
    def _run_command(self, command: List[str], timeout: int = 30) -> Tuple[bool, str, str]:
        """运行命令并返回结果"""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "命令超时"
        except Exception as e:
            return False, "", str(e)
    
    def _check_npm_package(self, package_name: str) -> bool:
        """检查npm包是否可用"""
        success, stdout, stderr = self._run_command(['npm', 'list', '-g', package_name])
        if success and package_name in stdout:
            return True
        
        # 尝试通过npx检查
        success, _, _ = self._run_command(['npx', '-y', package_name, '--help'], timeout=10)
        return success
    
    def _check_node_executable(self, path: str) -> bool:
        """检查Node.js可执行文件是否存在"""
        return Path(path).exists()
    
    def _validate_environment_vars(self, env_vars: Dict[str, str]) -> Dict[str, bool]:
        """验证环境变量"""
        results = {}
        for key, example_value in env_vars.items():
            actual_value = os.getenv(key)
            results[key] = actual_value is not None and actual_value != example_value
        return results
    
    def validate_server(self, server_name: str, server_config: Dict) -> Dict:
        """验证单个MCP服务器"""
        result = {
            'name': server_name,
            'description': server_config.get('description', ''),
            'command': server_config.get('command'),
            'args': server_config.get('args', []),
            'env': server_config.get('env', {}),
            'status': 'unknown',
            'issues': [],
            'recommendations': []
        }
        
        command = server_config.get('command')
        args = server_config.get('args', [])
        env_vars = server_config.get('env', {})
        
        # 检查命令可用性
        if command == 'npx':
            if len(args) >= 2:
                package_name = args[1]  # 跳过 -y 参数
                if self._check_npm_package(package_name):
                    result['status'] = 'available'
                else:
                    result['status'] = 'missing'
                    result['issues'].append(f"npm包 {package_name} 未安装")
                    result['recommendations'].append(f"运行: npm install -g {package_name}")
        
        elif command == 'node':
            if len(args) >= 1:
                executable_path = args[0]
                if self._check_node_executable(executable_path):
                    result['status'] = 'available'
                else:
                    result['status'] = 'missing'
                    result['issues'].append(f"可执行文件不存在: {executable_path}")
                    result['recommendations'].append(f"检查路径或安装相应的MCP服务器")
        
        # 检查环境变量
        if env_vars:
            env_status = self._validate_environment_vars(env_vars)
            missing_vars = [var for var, status in env_status.items() if not status]
            
            if missing_vars:
                if result['status'] == 'available':
                    result['status'] = 'needs_config'
                result['issues'].extend([f"环境变量 {var} 未设置" for var in missing_vars])
                result['recommendations'].extend([f"设置环境变量: export {var}=your_value" for var in missing_vars])
        
        return result
    
    def validate_all_servers(self) -> Dict[str, Dict]:
        """验证所有MCP服务器"""
        servers = self.config.get('mcpServers', {})
        results = {}
        
        print(f"{Colors.BOLD}{Colors.BLUE}🔍 开始验证 {len(servers)} 个MCP服务器...{Colors.END}\n")
        
        for server_name, server_config in servers.items():
            print(f"验证 {server_name}...", end=' ')
            result = self.validate_server(server_name, server_config)
            results[server_name] = result
            
            # 显示状态
            if result['status'] == 'available':
                print(f"{Colors.GREEN}✓ 可用{Colors.END}")
            elif result['status'] == 'needs_config':
                print(f"{Colors.YELLOW}⚠ 需要配置{Colors.END}")
            elif result['status'] == 'missing':
                print(f"{Colors.RED}✗ 缺失{Colors.END}")
            else:
                print(f"{Colors.PURPLE}? 未知{Colors.END}")
        
        return results
    
    def generate_report(self, results: Dict[str, Dict]):
        """生成验证报告"""
        print(f"\n{Colors.BOLD}{Colors.WHITE}📊 MCP服务器验证报告{Colors.END}")
        print("=" * 60)
        
        # 统计信息
        available = sum(1 for r in results.values() if r['status'] == 'available')
        needs_config = sum(1 for r in results.values() if r['status'] == 'needs_config')
        missing = sum(1 for r in results.values() if r['status'] == 'missing')
        total = len(results)
        
        print(f"\n{Colors.BOLD}总体状态:{Colors.END}")
        print(f"{Colors.GREEN}✓ 可直接使用: {available}{Colors.END}")
        print(f"{Colors.YELLOW}⚠ 需要配置: {needs_config}{Colors.END}")
        print(f"{Colors.RED}✗ 需要安装: {missing}{Colors.END}")
        print(f"{Colors.BLUE}📈 可用率: {(available + needs_config) / total * 100:.1f}%{Colors.END}")
        
        # 详细报告
        print(f"\n{Colors.BOLD}详细分析:{Colors.END}")
        
        # 按类别分组
        categories = {
            '🏆 可直接使用': [r for r in results.values() if r['status'] == 'available'],
            '⚠️ 需要配置': [r for r in results.values() if r['status'] == 'needs_config'],
            '❌ 需要安装': [r for r in results.values() if r['status'] == 'missing']
        }
        
        for category, servers in categories.items():
            if servers:
                print(f"\n{Colors.BOLD}{category} ({len(servers)}个):{Colors.END}")
                for server in servers:
                    print(f"  • {server['name']}: {server['description']}")
                    if server['issues']:
                        for issue in server['issues']:
                            print(f"    {Colors.RED}问题: {issue}{Colors.END}")
                    if server['recommendations']:
                        for rec in server['recommendations']:
                            print(f"    {Colors.CYAN}建议: {rec}{Colors.END}")
    
    def generate_priority_recommendations(self, results: Dict[str, Dict]):
        """生成优先级推荐"""
        print(f"\n{Colors.BOLD}{Colors.PURPLE}🎯 优先配置建议{Colors.END}")
        print("=" * 60)
        
        # 核心工具（高优先级）
        core_tools = ['filesystem', 'memory', 'github', 'fetch']
        # 开发工具（中优先级）
        dev_tools = ['brave-search', 'time', 'sequential-thinking']
        # 专业工具（低优先级）
        specialized_tools = ['postgres', 'sqlite', 'docker', 'kubernetes', 'aws']
        
        priorities = [
            ("🔥 核心工具 (立即配置)", core_tools),
            ("⭐ 开发工具 (推荐配置)", dev_tools),
            ("🔧 专业工具 (按需配置)", specialized_tools)
        ]
        
        for priority_name, tool_list in priorities:
            print(f"\n{Colors.BOLD}{priority_name}:{Colors.END}")
            for tool in tool_list:
                if tool in results:
                    server = results[tool]
                    status_color = Colors.GREEN if server['status'] == 'available' else Colors.YELLOW if server['status'] == 'needs_config' else Colors.RED
                    print(f"  • {tool}: {status_color}{server['status']}{Colors.END} - {server['description']}")
    
    def generate_installation_script(self, results: Dict[str, Dict]):
        """生成安装脚本"""
        missing_packages = []
        config_needed = []
        
        for server_name, result in results.items():
            if result['status'] == 'missing' and result['command'] == 'npx':
                if len(result['args']) >= 2:
                    package_name = result['args'][1]
                    missing_packages.append(package_name)
            elif result['status'] == 'needs_config':
                config_needed.append(server_name)
        
        if missing_packages or config_needed:
            print(f"\n{Colors.BOLD}{Colors.CYAN}🚀 快速安装脚本{Colors.END}")
            print("=" * 60)
            
            if missing_packages:
                print(f"\n{Colors.BOLD}1. 安装缺失的MCP服务器:{Colors.END}")
                print("```bash")
                for package in missing_packages:
                    print(f"npm install -g {package}")
                print("```")
            
            if config_needed:
                print(f"\n{Colors.BOLD}2. 配置环境变量:{Colors.END}")
                print("```bash")
                print("# 创建 .env 文件")
                print("cat > .env << 'EOF'")
                for server_name in config_needed:
                    server = results[server_name]
                    for env_var in server['env'].keys():
                        print(f"{env_var}=your_actual_value_here")
                print("EOF")
                print("")
                print("# 加载环境变量")
                print("source .env")
                print("```")

def main():
    """主函数"""
    config_path = "mcp/servers/claude-desktop-config.json"
    
    if not Path(config_path).exists():
        print(f"{Colors.RED}❌ 配置文件不存在: {config_path}{Colors.END}")
        sys.exit(1)
    
    validator = MCPServerValidator(config_path)
    results = validator.validate_all_servers()
    
    validator.generate_report(results)
    validator.generate_priority_recommendations(results)
    validator.generate_installation_script(results)
    
    print(f"\n{Colors.BOLD}{Colors.GREEN}✅ MCP服务器验证完成！{Colors.END}")

if __name__ == "__main__":
    main()
